require('dotenv').config();
const PlacesScraper = require('./scraper');

async function testGridHybrid() {
  console.log('🚀 Test hybride grid-based scraping (Places API + beperkte Firecrawl)...\n');

  // Amsterdam centrum coördinaten
  const center = { lat: 52.3676, lng: 4.9041 };
  const radiusKm = 1; // Kleine radius voor snelle test
  const keywords = ['restaurant']; // Restaurant

  console.log('📋 Test configuratie:');
  console.log(`📍 Locatie: Amsterdam centrum (${center.lat}, ${center.lng})`);
  console.log(`⭕ Radius: ${radiusKm} km`);
  console.log(`🔑 Zoektermen: ${keywords.join(', ')}`);
  console.log(`🔄 Hybride modus: Places API + beperkte Firecrawl (decision_maker_name, role_title, email, summary)\n`);
  console.log('');

  // Configureer scraper voor hybride modus
  const scraper = new PlacesScraper({
    delayBetweenRequests: 500, // Iets langzamer vanwege Firecrawl calls
    enableFirecrawlEnrichment: true, // Firecrawl ingeschakeld
    firecrawlMode: 'limited', // Hybride modus - alleen specifieke velden
    logLevel: 'info',
    maxPlacesPerGrid: 10 // Beperk voor test
  });

  try {
    console.log('🔍 Starten van hybride scraper...\n');
    
    await scraper.run({
      center: center,
      radiusKm: radiusKm,
      keywords: keywords,
      gridSizeM: 500
    });

    console.log('\n✅ Hybride test voltooid!');
    console.log('\n📊 Resultaten:');
    console.log(`- Totaal verwerkt: ${scraper.totalProcessed}`);
    console.log(`- Totaal opgeslagen: ${scraper.totalSaved}`);
    console.log(`- Totaal verrijkt: ${scraper.totalEnriched}`);
    console.log(`- Totaal fouten: ${scraper.totalErrors}`);
    
    console.log('\n💡 Hybride modus voordelen:');
    console.log('- Sneller dan volledige Firecrawl modus');
    console.log('- Meer contactgegevens dan alleen Places API');
    console.log('- Lagere kosten dan volledige verrijking');
    console.log('- Ideaal voor lead generatie met budget constraints');
    
  } catch (error) {
    console.error('\n❌ Fout tijdens hybride test:', error.message);
    console.error(error.stack);
  }
}

// Voer test uit als dit script direct wordt aangeroepen
if (require.main === module) {
  testGridHybrid();
}

module.exports = testGridHybrid;
